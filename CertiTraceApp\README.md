# CertiTrace Mobile App

A React Native mobile application for halal food traceability, designed to work with the CertiTrace Laravel backend.

## Features

- **Search Functionality**: Search for production lots across different stages (Production, Élevage, Sacrifice, Découpe, Fabrication, Conditionnement, Logistique)
- **Certificate Verification**: View detailed certificate information for each production stage
- **User Authentication**: Login and registration system
- **Responsive Design**: Modern UI with consistent theming
- **Offline Support**: Basic offline functionality for better user experience

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Android Studio (for Android development) or Xcode (for iOS development)
- Running CertiTrace Laravel backend

## Installation

1. **Clone the repository** (if not already done):
   ```bash
   cd CertiTraceApp
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure API endpoint**:
   - Open `src/config/api.js`
   - Update `BASE_URL` with your Laravel backend URL:
     ```javascript
     BASE_URL: 'http://YOUR_IP_ADDRESS:8000'
     ```
   - For testing on physical device, use your computer's local IP address
   - For Android emulator, you can use `http://********:8000`
   - For iOS simulator, you can use `http://localhost:8000`

## Running the App

### Development Mode

1. **Start the Expo development server**:
   ```bash
   npm start
   ```

2. **Run on specific platform**:
   ```bash
   # Android
   npm run android
   
   # iOS (macOS only)
   npm run ios
   
   # Web
   npm run web
   ```

### Testing on Physical Device

1. Install the Expo Go app on your device
2. Scan the QR code displayed in the terminal or browser
3. Make sure your device and computer are on the same network

## Project Structure

```
src/
├── components/          # Reusable UI components
├── config/             # Configuration files
├── constants/          # App constants and themes
├── navigation/         # Navigation configuration
├── screens/           # Screen components
├── services/          # API services
└── utils/             # Utility functions
```

## Key Screens

- **HomeScreen**: Main dashboard with search functionality
- **SearchScreen**: Advanced search with filters
- **SearchResultsScreen**: Display search results
- **DetailScreen**: Show detailed certificate information
- **LoginScreen**: User authentication
- **RegisterScreen**: User registration
- **ProfileScreen**: User profile and settings

## API Integration

The app integrates with the Laravel backend through the following endpoints:

- `POST /home_search` - Search for production lots
- `GET /productions-certificat/{id}` - Get production certificate
- `GET /elevages-certificat/{id}` - Get breeding certificate
- `GET /sacrifices-certificat/{id}` - Get sacrifice certificate
- `GET /decoupes-certificat/{id}` - Get cutting certificate
- `GET /fabrications-certificat/{id}` - Get fabrication certificate
- `GET /conditionnement-certificat/{id}` - Get packaging certificate
- `GET /logistique-certificat/{id}` - Get logistics certificate

## Customization

### Theming

Colors and themes can be customized in `src/constants/colors.js`:

```javascript
export const colors = {
  primary: '#2E7D32',      // Main brand color
  secondary: '#FF9800',    // Accent color
  // ... other colors
};
```

### API Configuration

Update API settings in `src/config/api.js`:

```javascript
export const API_CONFIG = {
  BASE_URL: 'your-backend-url',
  TIMEOUT: 10000,
  // ... other settings
};
```

## Building for Production

### Android

1. **Build APK**:
   ```bash
   expo build:android
   ```

2. **Build AAB** (for Google Play Store):
   ```bash
   expo build:android -t app-bundle
   ```

### iOS

1. **Build IPA**:
   ```bash
   expo build:ios
   ```

## Troubleshooting

### Common Issues

1. **Network Error**: Make sure the Laravel backend is running and accessible
2. **Metro bundler issues**: Clear cache with `expo start -c`
3. **Android build issues**: Make sure Android SDK is properly configured
4. **iOS build issues**: Ensure Xcode is installed and configured

### Backend Connection

If you're having trouble connecting to the Laravel backend:

1. Check if the backend server is running
2. Verify the IP address in `src/config/api.js`
3. Make sure your device/emulator can reach the backend
4. Check firewall settings on your development machine

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to documentation]
- Issues: [Link to GitHub issues]
