import axios from 'axios';
import { API_CONFIG } from '../config/api';

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token if available
api.interceptors.request.use(
  (config) => {
    // You can add authentication token here if needed
    // const token = AsyncStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      // You might want to redirect to login screen
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const apiEndpoints = {
  // Search endpoint
  search: (query) => api.post(API_CONFIG.ENDPOINTS.SEARCH, { search: query }),

  // Authentication endpoints (if you implement them in Laravel)
  login: (credentials) => api.post(API_CONFIG.ENDPOINTS.LOGIN, credentials),
  register: (userData) => api.post(API_CONFIG.ENDPOINTS.REGISTER, userData),
  logout: () => api.post(API_CONFIG.ENDPOINTS.LOGOUT),

  // Get certificate details
  getProductionCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.PRODUCTION_CERT}/${id}`),
  getElevageCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.ELEVAGE_CERT}/${id}`),
  getSacrificeCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.SACRIFICE_CERT}/${id}`),
  getDecoupeCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.DECOUPE_CERT}/${id}`),
  getFabricationCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.FABRICATION_CERT}/${id}`),
  getConditionnementCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.CONDITIONNEMENT_CERT}/${id}`),
  getLogistiqueCertificate: (id) => api.get(`${API_CONFIG.ENDPOINTS.LOGISTIQUE_CERT}/${id}`),
};

export default api;
