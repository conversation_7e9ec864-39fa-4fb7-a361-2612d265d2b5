// API Configuration
// Update these values according to your Laravel backend setup

export const API_CONFIG = {
  // For development with local Laravel server
  // Replace with your computer's IP address if testing on physical device
  BASE_URL: 'http://*************:8000',
  
  // For production
  // BASE_URL: 'https://your-domain.com',
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // API endpoints
  ENDPOINTS: {
    SEARCH: '/home_search',
    LOGIN: '/api/login',
    REGISTER: '/api/register',
    LOGOUT: '/api/logout',
    
    // Certificate endpoints
    PRODUCTION_CERT: '/productions-certificat',
    ELEVAGE_CERT: '/elevages-certificat',
    SACRIFICE_CERT: '/sacrifices-certificat',
    DECOUPE_CERT: '/decoupes-certificat',
    FABRICATION_CERT: '/fabrications-certificat',
    CONDITIONNEMENT_CERT: '/conditionnement-certificat',
    LOGISTIQUE_CERT: '/logistique-certificat',
  },
};

// Instructions for setup:
// 1. Make sure your Laravel backend is running
// 2. Update BASE_URL with your server's IP address
// 3. For physical device testing, use your computer's local IP
// 4. For emulator testing, you can use 'http://********:8000' (Android) or 'http://localhost:8000' (iOS Simulator)
