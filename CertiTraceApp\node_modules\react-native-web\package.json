{"publishConfig": {"registry": "https://registry.npmjs.org/"}, "name": "react-native-web", "version": "0.20.0", "description": "React Native for Web", "module": "dist/index.js", "main": "dist/cjs/index.js", "sideEffects": false, "files": ["dist", "src", "!**/__tests__"], "scripts": {"clean": "del-cli ./dist", "build": "npm-run-all clean -p \"build:* {@}\" --", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --config-file ../../configs/babel.config.js --out-dir dist/cjs --ignore \"**/__tests__\"", "build:es": "babel src --config-file ../../configs/babel.config.js --out-dir dist --ignore \"**/__tests__\"", "build:flow": "gen-flow-files ./src --out-dir ./dist", "dev": "npm-run-all clean -p \"build:* {@} -- --watch\""}, "dependencies": {"@babel/runtime": "^7.18.6", "@react-native/normalize-colors": "^0.74.1", "fbjs": "^3.0.4", "inline-style-prefixer": "^7.0.1", "memoize-one": "^6.0.0", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "styleq": "^0.1.3"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/necolas/react-native-web.git"}, "tags": ["react"], "keywords": ["react", "react-component", "react-native", "web"]}