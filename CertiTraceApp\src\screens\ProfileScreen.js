import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, SCREEN_NAMES } from '../constants';

const ProfileScreen = ({ navigation }) => {
  const [user, setUser] = useState({
    name: 'Utilisateur Invité',
    email: '<EMAIL>',
    company: 'Non spécifié',
    isGuest: true,
  });

  const handleLogin = () => {
    navigation.navigate(SCREEN_NAMES.LOGIN);
  };

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Déconnexion', 
          style: 'destructive',
          onPress: () => {
            // Handle logout logic here
            setUser({
              name: 'Utilisateur Invité',
              email: '<EMAIL>',
              company: 'Non spécifié',
              isGuest: true,
            });
          }
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'À propos de CertiTrace',
      'CertiTrace v1.0.0\n\nApplication mobile de traçabilité halal développée pour assurer la transparence et la vérification des produits tout au long de la chaîne de production.\n\n© 2024 CertiTrace. Tous droits réservés.'
    );
  };

  const handleContact = () => {
    Alert.alert(
      'Contact',
      'Pour toute question ou assistance :\n\nEmail: <EMAIL>\nTéléphone: +216 XX XXX XXX\n\nNous sommes là pour vous aider !'
    );
  };

  const menuItems = [
    {
      icon: 'person-outline',
      title: 'Informations personnelles',
      subtitle: 'Gérer vos informations',
      onPress: () => Alert.alert('Info', 'Fonctionnalité à venir'),
    },
    {
      icon: 'notifications-outline',
      title: 'Notifications',
      subtitle: 'Paramètres de notification',
      onPress: () => Alert.alert('Info', 'Fonctionnalité à venir'),
    },
    {
      icon: 'shield-checkmark-outline',
      title: 'Sécurité',
      subtitle: 'Mot de passe et sécurité',
      onPress: () => Alert.alert('Info', 'Fonctionnalité à venir'),
    },
    {
      icon: 'language-outline',
      title: 'Langue',
      subtitle: 'Français',
      onPress: () => Alert.alert('Info', 'Fonctionnalité à venir'),
    },
    {
      icon: 'help-circle-outline',
      title: 'Aide',
      subtitle: 'FAQ et support',
      onPress: handleContact,
    },
    {
      icon: 'information-circle-outline',
      title: 'À propos',
      subtitle: 'Version et informations',
      onPress: handleAbout,
    },
  ];

  const renderMenuItem = (item, index) => (
    <TouchableOpacity
      key={index}
      style={styles.menuItem}
      onPress={item.onPress}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons name={item.icon} size={24} color={colors.primary} />
        <View style={styles.menuItemText}>
          <Text style={styles.menuItemTitle}>{item.title}</Text>
          <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <Ionicons 
            name={user.isGuest ? "person-outline" : "person"} 
            size={48} 
            color={colors.textLight} 
          />
        </View>
        <Text style={styles.userName}>{user.name}</Text>
        <Text style={styles.userEmail}>{user.email}</Text>
        {user.company && (
          <Text style={styles.userCompany}>{user.company}</Text>
        )}
        
        {user.isGuest ? (
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginButtonText}>Se connecter</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.statusBadge}>
            <Ionicons name="checkmark-circle" size={16} color={colors.success} />
            <Text style={styles.statusText}>Compte vérifié</Text>
          </View>
        )}
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => renderMenuItem(item, index))}
      </View>

      {/* Logout Button */}
      {!user.isGuest && (
        <View style={styles.logoutContainer}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={20} color={colors.error} />
            <Text style={styles.logoutButtonText}>Déconnexion</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* App Version */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  profileHeader: {
    backgroundColor: colors.primary,
    padding: 30,
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryDark,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textLight,
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 16,
    color: colors.textLight,
    opacity: 0.9,
    marginBottom: 5,
  },
  userCompany: {
    fontSize: 14,
    color: colors.textLight,
    opacity: 0.8,
    marginBottom: 15,
  },
  loginButton: {
    backgroundColor: colors.textLight,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  loginButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.textLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  statusText: {
    color: colors.success,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 5,
  },
  menuContainer: {
    margin: 20,
    backgroundColor: colors.surface,
    borderRadius: 15,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    marginLeft: 15,
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  logoutContainer: {
    margin: 20,
    marginTop: 0,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    borderRadius: 10,
    padding: 15,
    borderWidth: 1,
    borderColor: colors.error,
  },
  logoutButtonText: {
    color: colors.error,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  versionContainer: {
    alignItems: 'center',
    padding: 20,
  },
  versionText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
});

export default ProfileScreen;
